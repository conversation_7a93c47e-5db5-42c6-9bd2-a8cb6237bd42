package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceSpecialties;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-25T16:07:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRytcToPoliceSpecialtiesMapperImpl implements VWjRytcToPoliceSpecialtiesMapper {

    @Override
    public PoliceSpecialties convert(VWjRytc source) {
        if ( source == null ) {
            return null;
        }

        PoliceSpecialties policeSpecialties = new PoliceSpecialties();

        policeSpecialties.setApproveAuthority( source.getJcpjjgmc() );
        policeSpecialties.setSpecialtyName( source.getJcmc() );
        policeSpecialties.setIdCard( source.getGmsfhm() );
        policeSpecialties.setAwardDate( ConversionUtils.strToDate( source.getJcsj() ) );

        return policeSpecialties;
    }

    @Override
    public PoliceSpecialties convert(VWjRytc source, PoliceSpecialties target) {
        if ( source == null ) {
            return target;
        }

        target.setApproveAuthority( source.getJcpjjgmc() );
        target.setSpecialtyName( source.getJcmc() );
        target.setIdCard( source.getGmsfhm() );
        target.setAwardDate( ConversionUtils.strToDate( source.getJcsj() ) );

        return target;
    }
}
