package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceProjectContact;
import com.hl.archive.domain.entity.PoliceProjectContactToVWjXhjhRxgrPylxMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__381;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__381.class,
    uses = {ConversionUtils.class,PoliceProjectContactToVWjXhjhRxgrPylxMapper.class},
    imports = {}
)
public interface VWjXhjhRxgrPylxToPoliceProjectContactMapper extends BaseMapper<VWjXhjhRxgrPylx, PoliceProjectContact> {
  @Mapping(
      target = "evaluation",
      source = "pypj"
  )
  @Mapping(
      target = "traits",
      source = "tzld"
  )
  @Mapping(
      target = "registeredBy",
      source = "djrxm"
  )
  @Mapping(
      target = "registerTime",
      source = "djsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "contactName",
      source = "pylxrXm"
  )
  @Mapping(
      target = "contactPosition",
      source = "pylxrZw"
  )
  @Mapping(
      target = "xhjhZjbh",
      source = "jlXxzjbh"
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceProjectContact convert(VWjXhjhRxgrPylx source);

  @Mapping(
      target = "evaluation",
      source = "pypj"
  )
  @Mapping(
      target = "traits",
      source = "tzld"
  )
  @Mapping(
      target = "registeredBy",
      source = "djrxm"
  )
  @Mapping(
      target = "registerTime",
      source = "djsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "contactName",
      source = "pylxrXm"
  )
  @Mapping(
      target = "contactPosition",
      source = "pylxrZw"
  )
  @Mapping(
      target = "xhjhZjbh",
      source = "jlXxzjbh"
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceProjectContact convert(VWjXhjhRxgrPylx source, @MappingTarget PoliceProjectContact target);
}
