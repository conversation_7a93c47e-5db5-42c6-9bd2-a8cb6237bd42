package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyCriminalLiability;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-25T16:07:59+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjZnXszjToPoliceFamilyCriminalLiabilityMapperImpl implements VWjZnXszjToPoliceFamilyCriminalLiabilityMapper {

    @Override
    public PoliceFamilyCriminalLiability convert(VWjZnXszj source) {
        if ( source == null ) {
            return null;
        }

        PoliceFamilyCriminalLiability policeFamilyCriminalLiability = new PoliceFamilyCriminalLiability();

        policeFamilyCriminalLiability.setProsecutionDate( ConversionUtils.strToDate( source.getBzjsj() ) );
        policeFamilyCriminalLiability.setIdCard( source.getGmsfhm() );
        policeFamilyCriminalLiability.setHandlingStage( source.getCljd() );
        policeFamilyCriminalLiability.setName( source.getXmDsr() );
        policeFamilyCriminalLiability.setProsecutionReason( source.getBzjyy() );
        policeFamilyCriminalLiability.setHandlingResult( source.getCljg() );

        return policeFamilyCriminalLiability;
    }

    @Override
    public PoliceFamilyCriminalLiability convert(VWjZnXszj source, PoliceFamilyCriminalLiability target) {
        if ( source == null ) {
            return target;
        }

        target.setProsecutionDate( ConversionUtils.strToDate( source.getBzjsj() ) );
        target.setIdCard( source.getGmsfhm() );
        target.setHandlingStage( source.getCljd() );
        target.setName( source.getXmDsr() );
        target.setProsecutionReason( source.getBzjyy() );
        target.setHandlingResult( source.getCljg() );

        return target;
    }
}
