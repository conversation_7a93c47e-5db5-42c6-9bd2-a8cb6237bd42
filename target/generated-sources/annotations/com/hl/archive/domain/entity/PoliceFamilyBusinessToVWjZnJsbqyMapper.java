package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjZnJsbqy;
import com.hl.orasync.domain.VWjZnJsbqyToPoliceFamilyBusinessMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__381;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__381.class,
    uses = {ConversionUtils.class,VWjZnJsbqyToPoliceFamilyBusinessMapper.class},
    imports = {}
)
public interface PoliceFamilyBusinessToVWjZnJsbqyMapper extends BaseMapper<PoliceFamilyBusiness, VWjZnJsbqy> {
  @Mapping(
      target = "grcze",
      source = "personalContribution"
  )
  @Mapping(
      target = "gjzwmc",
      source = "seniorPositionName"
  )
  @Mapping(
      target = "sfgd",
      source = "isShareholder"
  )
  @Mapping(
      target = "qylxmc",
      source = "enterpriseType"
  )
  @Mapping(
      target = "gjzwsj",
      source = "seniorPositionDate"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "zczb",
      source = "registeredCapital"
  )
  @Mapping(
      target = "qymc",
      source = "enterpriseName"
  )
  @Mapping(
      target = "tzsj",
      source = "investmentDate"
  )
  @Mapping(
      target = "qyztmc",
      source = "enterpriseStatus"
  )
  @Mapping(
      target = "clsj",
      source = "establishmentDate"
  )
  @Mapping(
      target = "jyfw",
      source = "businessScope"
  )
  @Mapping(
      target = "grczbl",
      source = "personalContributionRatio"
  )
  @Mapping(
      target = "jyd",
      source = "businessAddress"
  )
  @Mapping(
      target = "xmFr",
      source = "name"
  )
  @Mapping(
      target = "zcd",
      source = "registrationAddress"
  )
  @Mapping(
      target = "zch",
      source = "socialCreditCode"
  )
  @Mapping(
      target = "sfdrgjzw",
      source = "isSeniorPosition"
  )
  VWjZnJsbqy convert(PoliceFamilyBusiness source);

  @Mapping(
      target = "grcze",
      source = "personalContribution"
  )
  @Mapping(
      target = "gjzwmc",
      source = "seniorPositionName"
  )
  @Mapping(
      target = "sfgd",
      source = "isShareholder"
  )
  @Mapping(
      target = "qylxmc",
      source = "enterpriseType"
  )
  @Mapping(
      target = "gjzwsj",
      source = "seniorPositionDate"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "zczb",
      source = "registeredCapital"
  )
  @Mapping(
      target = "qymc",
      source = "enterpriseName"
  )
  @Mapping(
      target = "tzsj",
      source = "investmentDate"
  )
  @Mapping(
      target = "qyztmc",
      source = "enterpriseStatus"
  )
  @Mapping(
      target = "clsj",
      source = "establishmentDate"
  )
  @Mapping(
      target = "jyfw",
      source = "businessScope"
  )
  @Mapping(
      target = "grczbl",
      source = "personalContributionRatio"
  )
  @Mapping(
      target = "jyd",
      source = "businessAddress"
  )
  @Mapping(
      target = "xmFr",
      source = "name"
  )
  @Mapping(
      target = "zcd",
      source = "registrationAddress"
  )
  @Mapping(
      target = "zch",
      source = "socialCreditCode"
  )
  @Mapping(
      target = "sfdrgjzw",
      source = "isSeniorPosition"
  )
  VWjZnJsbqy convert(PoliceFamilyBusiness source, @MappingTarget VWjZnJsbqy target);
}
