package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBzjlDwry;
import com.hl.orasync.domain.VWjBzjlDwryToPoliceUnitAwardMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__381;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__381.class,
    uses = {ConversionUtils.class,VWjBzjlDwryToPoliceUnitAwardMapper.class},
    imports = {}
)
public interface PoliceUnitAwardToVWjBzjlDwryMapper extends BaseMapper<PoliceUnitAward, VWjBzjlDwry> {
  @Mapping(
      target = "jljgmc",
      source = "awardOrgan"
  )
  @Mapping(
      target = "bzsj",
      source = "awardTime"
  )
  @Mapping(
      target = "xm",
      source = "supervisorName"
  )
  @Mapping(
      target = "dwmc",
      source = "unit"
  )
  @Mapping(
      target = "bzwh",
      source = "documentNumber"
  )
  @Mapping(
      target = "jlmc",
      source = "awardName"
  )
  @Mapping(
      target = "jh",
      source = "supervisorCode"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  VWjBzjlDwry convert(PoliceUnitAward source);

  @Mapping(
      target = "jljgmc",
      source = "awardOrgan"
  )
  @Mapping(
      target = "bzsj",
      source = "awardTime"
  )
  @Mapping(
      target = "xm",
      source = "supervisorName"
  )
  @Mapping(
      target = "dwmc",
      source = "unit"
  )
  @Mapping(
      target = "bzwh",
      source = "documentNumber"
  )
  @Mapping(
      target = "jlmc",
      source = "awardName"
  )
  @Mapping(
      target = "jh",
      source = "supervisorCode"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  VWjBzjlDwry convert(PoliceUnitAward source, @MappingTarget VWjBzjlDwry target);
}
