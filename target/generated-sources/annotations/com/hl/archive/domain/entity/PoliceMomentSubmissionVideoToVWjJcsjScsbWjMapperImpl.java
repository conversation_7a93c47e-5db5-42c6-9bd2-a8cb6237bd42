package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjJcsjScsbWj;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-25T16:07:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceMomentSubmissionVideoToVWjJcsjScsbWjMapperImpl implements PoliceMomentSubmissionVideoToVWjJcsjScsbWjMapper {

    @Override
    public VWjJcsjScsbWj convert(PoliceMomentSubmissionVideo source) {
        if ( source == null ) {
            return null;
        }

        VWjJcsjScsbWj vWjJcsjScsbWj = new VWjJcsjScsbWj();

        vWjJcsjScsbWj.setFjcl2( source.getFileUrl() );
        vWjJcsjScsbWj.setWjlxmc( source.getFileType() );
        vWjJcsjScsbWj.setSbXxzjbh( source.getSbZjbh() );
        vWjJcsjScsbWj.setWjmc( source.getFileName() );
        vWjJcsjScsbWj.setXxzjbh( source.getZjbh() );

        return vWjJcsjScsbWj;
    }

    @Override
    public VWjJcsjScsbWj convert(PoliceMomentSubmissionVideo source, VWjJcsjScsbWj target) {
        if ( source == null ) {
            return target;
        }

        target.setFjcl2( source.getFileUrl() );
        target.setWjlxmc( source.getFileType() );
        target.setSbXxzjbh( source.getSbZjbh() );
        target.setWjmc( source.getFileName() );
        target.setXxzjbh( source.getZjbh() );

        return target;
    }
}
