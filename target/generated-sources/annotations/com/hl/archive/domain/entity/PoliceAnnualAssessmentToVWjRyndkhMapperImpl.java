package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyndkh;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-25T16:07:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceAnnualAssessmentToVWjRyndkhMapperImpl implements PoliceAnnualAssessmentToVWjRyndkhMapper {

    @Override
    public VWjRyndkh convert(PoliceAnnualAssessment source) {
        if ( source == null ) {
            return null;
        }

        VWjRyndkh vWjRyndkh = new VWjRyndkh();

        vWjRyndkh.setGmsfhm( source.getIdCard() );
        vWjRyndkh.setKhjg( source.getAssessmentResult() );
        vWjRyndkh.setKcnd( source.getAssessmentYear() );
        vWjRyndkh.setKclb( source.getAssessmentCategory() );

        return vWjRyndkh;
    }

    @Override
    public VWjRyndkh convert(PoliceAnnualAssessment source, VWjRyndkh target) {
        if ( source == null ) {
            return target;
        }

        target.setGmsfhm( source.getIdCard() );
        target.setKhjg( source.getAssessmentResult() );
        target.setKcnd( source.getAssessmentYear() );
        target.setKclb( source.getAssessmentCategory() );

        return target;
    }
}
