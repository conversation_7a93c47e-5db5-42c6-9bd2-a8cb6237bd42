package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjZnXszj;
import com.hl.orasync.domain.VWjZnXszjToPoliceFamilyCriminalLiabilityMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__381;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__381.class,
    uses = {ConversionUtils.class,VWjZnXszjToPoliceFamilyCriminalLiabilityMapper.class},
    imports = {}
)
public interface PoliceFamilyCriminalLiabilityToVWjZnXszjMapper extends BaseMapper<PoliceFamilyCriminalLiability, VWjZnXszj> {
  @Mapping(
      target = "xmDsr",
      source = "name"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "bzjsj",
      source = "prosecutionDate"
  )
  @Mapping(
      target = "cljg",
      source = "handlingResult"
  )
  @Mapping(
      target = "bzjyy",
      source = "prosecutionReason"
  )
  @Mapping(
      target = "cljd",
      source = "handlingStage"
  )
  VWjZnXszj convert(PoliceFamilyCriminalLiability source);

  @Mapping(
      target = "xmDsr",
      source = "name"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "bzjsj",
      source = "prosecutionDate"
  )
  @Mapping(
      target = "cljg",
      source = "handlingResult"
  )
  @Mapping(
      target = "bzjyy",
      source = "prosecutionReason"
  )
  @Mapping(
      target = "cljd",
      source = "handlingStage"
  )
  VWjZnXszj convert(PoliceFamilyCriminalLiability source, @MappingTarget VWjZnXszj target);
}
