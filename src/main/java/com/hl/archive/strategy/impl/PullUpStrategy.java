package com.hl.archive.strategy.impl;

import com.hl.archive.strategy.QualificationRecordStrategy;
import org.springframework.stereotype.Component;

@Component
public class PullUpStrategy implements QualificationRecordStrategy {
    @Override
    public String getProject() {
        return "引体向上";
    }

    @Override
    public double calculatePoint(int age, String rawScore) {
        int score = Integer.parseInt(rawScore);
        if (age <= 24) {
            if (score < 6) {
                return 0;
            }
            if (score == 6) {
                return 60;
            }
            if (score == 7) {
                return 65;
            }
            if (score == 9) {
                return 70;
            }
            if (score == 10) {
                return 75;
            }
            if (score == 11) {
                return 80;
            }
            if (score == 12) {
                return 85;
            }
            if (score == 13) {
                return 90;
            }
            if (score == 14) {
                return 95;
            }
            if (score >= 15) {
                return 100;
            }
        }
        if (age > 24 && age <= 29) {
            if (score < 4) {
                return 0;
            }
            if (score == 4) {
                return 60;
            }
            if (score == 5) {
                return 65;
            }
            if (score == 6) {
                return 70;
            }
            if (score == 7) {
                return 80;
            }
            if (score == 8) {
                return 85;
            }
            if (score == 9) {
                return 90;
            }
            if (score == 10) {
                return 95;
            }
            return 100;
        }
        if (age > 29 && age <= 34) {
            if (score < 4) {
                return 0;
            }
            if (score == 4) {
                return 60;
            }
            if (score == 5) {
                return 70;
            }
            if (score == 6) {
                return 80;
            }
            if (score == 7) {
                return 85;
            }
            if (score == 8) {
                return 90;
            }
            if (score == 9) {
                return 95;
            }
            return 100;
        }
        if (age > 34 && age <= 39) {
            if (score < 3) {
                return 0;
            }
            if (score == 3) {
                return 60;
            }
            if (score == 4) {
                return 70;
            }
            if (score == 5) {
                return 80;
            }
            if (score == 6) {
                return 90;
            }
            return 100;
        }
        return 100;

    }
}
