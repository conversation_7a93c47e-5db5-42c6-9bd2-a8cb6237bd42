package com.hl.archive.controller;

import com.hl.archive.service.PoliceLeaveRecordService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/leaveRecord")
@RequiredArgsConstructor
@Api(tags = "民警请休假数据汇总")
@Slf4j
public class PoliceLeaveRecordController {

    private final PoliceLeaveRecordService policeLeaveRecordService;


    @PostMapping("/countLeaveRecord")
    @ApiOperation("统计请休假数据")
    public R<?> countLeaveRecord() {

        return R.ok();
    }
}
