package com.hl.archive.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceClubActivityQueryDTO;
import com.hl.archive.domain.entity.PoliceClubInfo;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.listener.config.PoliceClubActivityConfig;
import com.hl.archive.mapper.PoliceClubInfoMapper;
import com.hl.common.domain.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.PoliceClubActivityMapper;
import com.hl.archive.domain.entity.PoliceClubActivity;

@Service
@RequiredArgsConstructor
@Slf4j
public class PoliceClubActivityService extends ServiceImpl<PoliceClubActivityMapper, PoliceClubActivity> {


    private final PoliceClubInfoMapper policeClubInfoMapper;

    private final TaskApi taskApi;

    @Value("${spring.security.sso.projectToken}")
    private String token;

    private final PoliceClubActivityConfig policeClubActivityConfig;

    public Page<PoliceClubActivity> pageList(PoliceClubActivityQueryDTO requestDTO) {
        Page<PoliceClubActivity> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), Wrappers.<PoliceClubActivity>lambdaQuery()
                .like(StrUtil.isNotBlank(requestDTO.getLocation()), PoliceClubActivity::getLocation, requestDTO.getLocation())
                .like(StrUtil.isNotBlank(requestDTO.getActivityTime()), PoliceClubActivity::getActivityTime, requestDTO)
                .eq(requestDTO.getClubId() != null, PoliceClubActivity::getClubId, requestDTO.getClubId()));

        // 获取社团信息
        List<Long> clubIdList = page.getRecords().stream().map(PoliceClubActivity::getClubId).collect(Collectors.toList());
        if (!clubIdList.isEmpty()) {
            List<PoliceClubInfo> clubInfoList = policeClubInfoMapper.selectBatchIds(clubIdList);
            Map<Long, PoliceClubInfo> policeClubInfoMap = clubInfoList.stream()
                    .collect(Collectors.toMap(PoliceClubInfo::getId, c -> c));
            page.getRecords().forEach(r -> r.setClubInfo(policeClubInfoMap.get(r.getClubId())));
        }

        return page;
    }

    public void saveClubActivity(JSONObject contentData) {
        try {
            String customId = contentData.getByPath("data.custom_id").toString();
            if (!"ZZCSP".equals(customId)) return;

            String passStatus = contentData.getByPath("data.content.pass").toString();
            if (!"1".equals(passStatus)) return;

            String taskId = contentData.getByPath("data.task_id").toString();
            JSONObject param = new JSONObject();
            param.put("task_id", taskId);
            R<?> oneTask = taskApi.getOneTask(token, param);
            JSONObject parsed = JSONObject.from(oneTask.getData());
            JSONObject content = parsed.getJSONObject("all_content");
            PoliceClubActivity clubActivity = new PoliceClubActivity();
            clubActivity.setClubId(Long.valueOf(content.getString("ssst")));
            clubActivity.setActivityTime(DateUtil.parse(content.getString(policeClubActivityConfig.getActivityTime())).toLocalDateTime());
            clubActivity.setLocation(content.getString(policeClubActivityConfig.getLocation()));
            clubActivity.setParticipants(content.getList(policeClubActivityConfig.getParticipants(),String.class));
            clubActivity.setAdvancePayment("有".equals(content.getString(policeClubActivityConfig.getAdvancePayment())) ? "1" : "0");
            clubActivity.setActivityName(content.getString(policeClubActivityConfig.getActivityName()));
            clubActivity.setActivityBudget(content.getString(policeClubActivityConfig.getActivityBudget()));
            clubActivity.setCreatedBy(content.getString("id_card"));
            clubActivity.setTaskId(taskId);
            this.save(clubActivity);
            log.info("社团活动保存成功:{}", clubActivity);
        } catch (NumberFormatException e) {
            log.error("社团活动保存失败");
            log.error(e.getMessage(), e);
        }
    }

    public void updateClubActivityMedia(JSONObject contentData) {
        try {
            String taskId = contentData.getByPath("data.task_id").toString();
            JSONObject param = new JSONObject();
            param.put("task_id", taskId);
            R<?> oneTask = taskApi.getOneTask(token, param);
            JSONObject parsed = JSONObject.from(oneTask.getData());
            JSONObject content = parsed.getJSONObject("all_content");

            PoliceClubActivity clubActivity = this.getOne(Wrappers.<PoliceClubActivity>lambdaQuery()
                    .eq(PoliceClubActivity::getTaskId, taskId));
            clubActivity.setMedia(content.getList(policeClubActivityConfig.getMedia(), JSONObject.class));
            clubActivity.setAttachment(content.getList(policeClubActivityConfig.getAttachment(), JSONObject.class));
            this.updateById(clubActivity);
        } catch (Exception e) {
            log.error("更新社团活动附件失败");
            log.error(e.getMessage(), e);
        }
    }

    public void deleteClubActivity(JSONObject contentData) {
        try {
            String taskId = contentData.getByPath("data.task_id").toString();
            this.remove(Wrappers.<PoliceClubActivity>lambdaQuery()
                    .eq(PoliceClubActivity::getTaskId, taskId));
            log.info("社团活动删除成功:{}", taskId);
        } catch (Exception e) {
            log.error("删除社团活动失败");
            log.error(e.getMessage(), e);
        }
    }
}
