package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * 社团活动表
 */
@ApiModel(description="社团活动表")
@Data
@TableName(value = "police_club_activity",autoResultMap = true)
public class PoliceClubActivity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 所属社团ID
     */
    @TableField(value = "club_id")
    @ApiModelProperty(value="所属社团ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long clubId;

    @TableField(exist = false)
    private PoliceClubInfo clubInfo;

    /**
     * 活动时间
     */
    @TableField(value = "activity_time")
    @ApiModelProperty(value="活动时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityTime;

    /**
     * 活动地点
     */
    @TableField(value = "`location`")
    @ApiModelProperty(value="活动地点")
    private String location;

    /**
     * 参与人
     */
    @TableField(value = "participants",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value="参与人")
    private List<String> participants;

    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARDS_TO_USER_LIST, mapper = "participants")
    private List<JSONObject> participantsInfo;

    /**
     * 附件
     */
    @TableField(value = "attachment",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value="附件")
    private List<JSONObject> attachment;

    /**
     * 活动照片/视频URL列表（可用JSON数组存储）
     */
    @TableField(value = "media",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value="活动照片/视频URL列表（可用JSON数组存储）")
    private List<JSONObject> media;

    /**
     * 有无预支（0=无, 1=有）
     */
    @TableField(value = "advance_payment")
    @ApiModelProperty(value="有无预支")
    private String advancePayment;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 是否删除（0=未删除, 1=已删除）
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="是否删除（0=未删除, 1=已删除）")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value="更新人")
    private String updatedBy;

    @TableField(value = "activity_name")
    private String activityName;

    @TableField(value = "activity_budget")
    private String activityBudget;

    @TableField(value = "task_id")
    private String taskId;
}